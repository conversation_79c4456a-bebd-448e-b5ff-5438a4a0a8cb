<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
  <string name="catalyst_reload" project="catalyst" translatable="false">Reload</string>
  <string name="catalyst_reload_error" project="catalyst" translatable="false">Failed to load bundle. Try restarting the bundler or reconnecting your device.</string>
  <string name="catalyst_change_bundle_location" project="catalyst" translatable="false">Change Bundle Location</string>
  <string name="catalyst_open_debugger_error" project="catalyst" translatable="false">Failed to open debugger. Please check that the dev server is running and reload the app.</string>
  <string name="catalyst_debug_open" project="catalyst" translatable="false">Open Debugger</string>
  <string name="catalyst_debug_connecting" project="catalyst" translatable="false">Connecting to debugger...</string>
  <string name="catalyst_debug_error" project="catalyst" translatable="false">Failed to connect to debugger!</string>
  <string name="catalyst_hot_reloading" project="catalyst" translatable="false">Enable Fast Refresh</string>
  <string name="catalyst_hot_reloading_stop" project="catalyst" translatable="false">Disable Fast Refresh</string>
  <string name="catalyst_hot_reloading_auto_disable" project="catalyst" translatable="false">Disabling Fast Refresh because it requires a development bundle.</string>
  <string name="catalyst_hot_reloading_auto_enable" project="catalyst" translatable="false">Switching to development bundle in order to enable Fast Refresh.</string>
  <string name="catalyst_inspector" project="catalyst" translatable="false">Show Element Inspector</string>
  <string name="catalyst_inspector_stop" project="catalyst" translatable="false">Hide Element Inspector</string>
  <string name="catalyst_perf_monitor" project="catalyst" translatable="false">Show Perf Monitor</string>
  <string name="catalyst_perf_monitor_stop" project="catalyst" translatable="false">Hide Perf Monitor</string>
  <string name="catalyst_settings" project="catalyst" translatable="false">Settings</string>
  <string name="catalyst_settings_title" project="catalyst" translatable="false">Debug Settings</string>
  <string name="catalyst_heap_capture" project="catalyst" translatable="false">Capture Heap</string>
  <string name="catalyst_dismiss_button" project="catalyst" translatable="false">Dismiss\n(ESC)</string>
  <string name="catalyst_reload_button" project="catalyst" translatable="false">Reload\n(R,\u00A0R)</string>
  <string name="catalyst_copy_button" project="catalyst" translatable="false">Copy\n</string>
  <string name="catalyst_report_button" project="catalyst" translatable="false">Report</string>
  <string name="catalyst_loading_from_url" project="catalyst" translatable="false">Loading from %1$s…</string>
  <string name="catalyst_sample_profiler_disable" project="catalyst" translatable="false">Disable Sampling Profiler</string>
  <string name="catalyst_sample_profiler_enable" project="catalyst" translatable="false">Enable Sampling Profiler</string>
  <string name="catalyst_dev_menu_header">React Native Dev Menu <xliff:g id="menu_header_name">(%1$s)</xliff:g></string>
  <string name="catalyst_dev_menu_sub_header">Running <xliff:g id="menu_sub_header_name">%1$s</xliff:g></string>
</resources>
